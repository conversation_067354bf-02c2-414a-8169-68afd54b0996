/**
 * Página de gerenciamento de templates de notificação
 * Lista, cria, edita e gerencia templates personalizáveis
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Edit, Copy, Trash2, <PERSON>otateCcw, Eye } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { useTemplateManagement } from '@/services/notifications';
import { TemplateEditor } from '@/components/notifications/templates/template-editor';
import type { 
  NotificationTemplate, 
  NotificationType,
  CreateTemplateData,
  UpdateTemplateData 
} from '@/services/notifications/types/notification-types';

const NOTIFICATION_TYPES = [
  { value: 'payment', label: 'Pagamento' },
  { value: 'class', label: 'Aula' },
  { value: 'enrollment', label: 'Matrícula' },
  { value: 'event', label: 'Evento' },
  { value: 'system', label: 'Sistema' }
] as const;

const CHANNEL_LABELS = {
  in_app: 'In-App',
  email: 'Email',
  whatsapp: 'WhatsApp'
} as const;

export default function TemplatesPage() {
  const [selectedType, setSelectedType] = useState<NotificationType | 'all'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [editingTemplate, setEditingTemplate] = useState<NotificationTemplate | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);

  const {
    templates,
    loading,
    error,
    loadTemplates,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    duplicateTemplate,
    resetToDefault,
    clearError
  } = useTemplateManagement();

  // Carregar templates ao montar o componente
  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]);

  // Filtrar templates
  const filteredTemplates = templates.filter(template => {
    const matchesType = selectedType === 'all' || template.type === selectedType;
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.body_template.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesType && matchesSearch;
  });

  const handleCreateTemplate = async (data: CreateTemplateData) => {
    const result = await createTemplate(data);
    if (result) {
      toast.success('Template criado com sucesso!');
      setShowCreateDialog(false);
    } else {
      toast.error('Erro ao criar template');
    }
  };

  const handleUpdateTemplate = async (data: UpdateTemplateData) => {
    if (!editingTemplate) return;
    
    const result = await updateTemplate(editingTemplate.id, data);
    if (result) {
      toast.success('Template atualizado com sucesso!');
      setShowEditDialog(false);
      setEditingTemplate(null);
    } else {
      toast.error('Erro ao atualizar template');
    }
  };

  const handleDeleteTemplate = async (template: NotificationTemplate) => {
    const success = await deleteTemplate(template.id);
    if (success) {
      toast.success('Template deletado com sucesso!');
    } else {
      toast.error('Erro ao deletar template');
    }
  };

  const handleDuplicateTemplate = async (template: NotificationTemplate) => {
    const newName = `${template.name} (Cópia)`;
    const result = await duplicateTemplate(template.id, newName);
    if (result) {
      toast.success('Template duplicado com sucesso!');
    } else {
      toast.error('Erro ao duplicar template');
    }
  };

  const handleResetTemplate = async (template: NotificationTemplate) => {
    const result = await resetToDefault(template.id);
    if (result) {
      toast.success('Template resetado para padrão!');
    } else {
      toast.error('Erro ao resetar template');
    }
  };

  const openEditDialog = (template: NotificationTemplate) => {
    setEditingTemplate(template);
    setShowEditDialog(true);
  };

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-red-600 mb-4">Erro ao carregar templates: {error}</p>
              <Button onClick={clearError}>Tentar Novamente</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Templates de Notificação</h1>
          <p className="text-gray-600">
            Gerencie templates personalizáveis para diferentes tipos de notificação
          </p>
        </div>
        
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Novo Template
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Criar Novo Template</DialogTitle>
            </DialogHeader>
            <TemplateEditor
              onSave={handleCreateTemplate}
              onCancel={() => setShowCreateDialog(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filtros */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <Input
                placeholder="Buscar templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={selectedType} onValueChange={(value) => setSelectedType(value as NotificationType | 'all')}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os tipos</SelectItem>
                {NOTIFICATION_TYPES.map(type => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Templates */}
      {loading ? (
        <Card>
          <CardContent className="p-6">
            <div className="text-center">Carregando templates...</div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {filteredTemplates.length === 0 ? (
            <Card>
              <CardContent className="p-6">
                <div className="text-center text-gray-500">
                  {searchTerm || selectedType !== 'all' 
                    ? 'Nenhum template encontrado com os filtros aplicados.'
                    : 'Nenhum template encontrado. Crie seu primeiro template!'
                  }
                </div>
              </CardContent>
            </Card>
          ) : (
            filteredTemplates.map((template) => (
              <Card key={template.id}>
                <CardContent className="p-6">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-lg font-semibold">{template.name}</h3>
                        <Badge variant="outline">
                          {NOTIFICATION_TYPES.find(t => t.value === template.type)?.label}
                        </Badge>
                        <Badge variant="secondary">
                          {CHANNEL_LABELS[template.channel]}
                        </Badge>
                        {template.is_default && (
                          <Badge variant="default">Padrão</Badge>
                        )}
                        {!template.is_active && (
                          <Badge variant="destructive">Inativo</Badge>
                        )}
                      </div>
                      
                      <p className="text-gray-600 text-sm mb-2">
                        Versão {template.version} • Criado em {new Date(template.created_at).toLocaleDateString('pt-BR')}
                      </p>
                      
                      <div className="bg-gray-50 p-3 rounded text-sm font-mono max-h-20 overflow-hidden">
                        {template.body_template.substring(0, 150)}
                        {template.body_template.length > 150 && '...'}
                      </div>
                    </div>
                    
                    <div className="flex gap-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditDialog(template)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDuplicateTemplate(template)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      
                      {template.parent_template_id && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleResetTemplate(template)}
                        >
                          <RotateCcw className="w-4 h-4" />
                        </Button>
                      )}
                      
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Deletar Template</AlertDialogTitle>
                            <AlertDialogDescription>
                              Tem certeza que deseja deletar o template "{template.name}"? 
                              Esta ação não pode ser desfeita.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancelar</AlertDialogCancel>
                            <AlertDialogAction 
                              onClick={() => handleDeleteTemplate(template)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Deletar
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      )}

      {/* Dialog de Edição */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar Template</DialogTitle>
          </DialogHeader>
          {editingTemplate && (
            <TemplateEditor
              template={editingTemplate}
              onSave={handleUpdateTemplate}
              onCancel={() => {
                setShowEditDialog(false);
                setEditingTemplate(null);
              }}
              isEditing
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
